{"name": "@onlook/utility", "description": "A utility library for Onlook", "main": "./src/index.ts", "type": "module", "module": "src/index.ts", "types": "src/index.ts", "version": "0.0.0", "private": true, "repository": {"type": "git", "url": "https://github.com/onlook-dev/onlook.git"}, "scripts": {"clean": "rm -rf node_modules", "lint": "eslint --fix .", "format": "prettier --write .", "typecheck": "tsc --noEmit"}, "keywords": ["onlook", "utility"], "author": {"name": "Onlook", "email": "<EMAIL>"}, "license": "Apache-2.0", "homepage": "https://onlook.com", "devDependencies": {"@onlook/typescript": "*", "@types/culori": "^2.0.4"}, "dependencies": {"@onlook/fonts": "*", "browser-image-compression": "^2.0.2", "culori": "^4.0.1", "free-email-domains": "^1.2.16", "is-subdir": "^1.2.0", "mime-lite": "^1.0.3", "normalize-url": "^8.0.1", "strip-ansi": "^7.1.0", "tailwind-merge": "^3.3.1", "tldts": "^6.1.82", "uuid": "^11.1.0"}}